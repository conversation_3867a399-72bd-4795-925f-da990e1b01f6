import React, { useState } from 'react'
import { BrowserRouter as Router } from 'react-router-dom'
import { AuthProvider, useAuth } from './contexts/AuthContext'
import Login from './components/Login'
import Layout from './components/Layout'
import Dashboard from './components/Dashboard'
import DoctorManagement from './components/DoctorManagement'

function AppContent() {
  const { user, loading } = useAuth()
  const [currentPage, setCurrentPage] = useState('dashboard')

  console.log('AppContent render - user:', user, 'loading:', loading)

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading application...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return <Login />
  }

  const renderCurrentPage = () => {
    switch (currentPage) {
      case 'dashboard':
        return <Dashboard />
      case 'doctors':
        return <DoctorManagement />
      case 'patients':
        return <div className="text-center py-12"><h2 className="text-2xl font-bold text-gray-900">Patient Management</h2><p className="text-gray-600 mt-2">Coming soon...</p></div>
      case 'appointments':
        return <div className="text-center py-12"><h2 className="text-2xl font-bold text-gray-900">Appointment Management</h2><p className="text-gray-600 mt-2">Coming soon...</p></div>
      case 'departments':
        return <div className="text-center py-12"><h2 className="text-2xl font-bold text-gray-900">Department Management</h2><p className="text-gray-600 mt-2">Coming soon...</p></div>
      case 'rooms':
        return <div className="text-center py-12"><h2 className="text-2xl font-bold text-gray-900">Room Management</h2><p className="text-gray-600 mt-2">Coming soon...</p></div>
      case 'users':
        return <div className="text-center py-12"><h2 className="text-2xl font-bold text-gray-900">User Management</h2><p className="text-gray-600 mt-2">Coming soon...</p></div>
      case 'profile':
        return <div className="text-center py-12"><h2 className="text-2xl font-bold text-gray-900">Profile Settings</h2><p className="text-gray-600 mt-2">Coming soon...</p></div>
      default:
        return <Dashboard />
    }
  }

  return (
    <Layout currentPage={currentPage} onPageChange={setCurrentPage}>
      {renderCurrentPage()}
    </Layout>
  )
}

function App() {
  console.log('App component rendering')
  
  return (
    <Router>
      <AuthProvider>
        <AppContent />
      </AuthProvider>
    </Router>
  )
}

export default App