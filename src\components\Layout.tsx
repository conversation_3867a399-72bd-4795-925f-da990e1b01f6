import React from 'react'
import { useAuth } from '../contexts/AuthContext'
import { 
  Users, 
  UserPlus, 
  Calendar, 
  Building2, 
  Bed, 
  Settings, 
  LogOut,
  Activity,
  Stethoscope
} from 'lucide-react'

interface LayoutProps {
  children: React.ReactNode
  currentPage: string
  onPageChange: (page: string) => void
}

const Layout: React.FC<LayoutProps> = ({ children, currentPage, onPageChange }) => {
  const { profile, signOut } = useAuth()

  const menuItems = [
    { id: 'dashboard', label: 'Dashboard', icon: Activity, roles: ['admin', 'doctor', 'patient'] },
    { id: 'appointments', label: 'Appointments', icon: Calendar, roles: ['admin', 'doctor', 'patient'] },
    { id: 'doctors', label: 'Doctors', icon: Stethoscope, roles: ['admin'] },
    { id: 'patients', label: 'Patients', icon: Users, roles: ['admin', 'doctor'] },
    { id: 'departments', label: 'Departments', icon: Building2, roles: ['admin'] },
    { id: 'rooms', label: 'Rooms', icon: Bed, roles: ['admin'] },
    { id: 'users', label: 'Users', icon: UserPlus, roles: ['admin'] },
    { id: 'profile', label: 'Profile', icon: Settings, roles: ['admin', 'doctor', 'patient'] },
  ]

  const visibleMenuItems = menuItems.filter(item => 
    item.roles.includes(profile?.role || 'patient')
  )

  const handleSignOut = async () => {
    try {
      await signOut()
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar */}
      <div className="w-64 bg-white shadow-lg">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
              <Activity className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">MedSystem</h1>
              <p className="text-sm text-gray-500 capitalize">{profile?.role}</p>
            </div>
          </div>
        </div>

        <nav className="p-4">
          <ul className="space-y-2">
            {visibleMenuItems.map((item) => {
              const Icon = item.icon
              return (
                <li key={item.id}>
                  <button
                    onClick={() => onPageChange(item.id)}
                    className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${
                      currentPage === item.id
                        ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                        : 'text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span className="font-medium">{item.label}</span>
                  </button>
                </li>
              )
            })}
          </ul>
        </nav>

        <div className="absolute bottom-0 left-0 right-0 w-64 p-4 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                <span className="text-sm font-medium text-gray-700">
                  {profile?.full_name?.charAt(0)}
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {profile?.full_name}
                </p>
                <p className="text-xs text-gray-500 truncate">
                  {profile?.email}
                </p>
              </div>
            </div>
            <button
              onClick={handleSignOut}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <LogOut className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        <main className="flex-1 p-8">
          {children}
        </main>
      </div>
    </div>
  )
}

export default Layout