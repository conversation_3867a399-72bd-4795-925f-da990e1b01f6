<?php
session_start();

// Demo login
if ($_POST['login'] ?? false) {
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    
    $demo_accounts = [
        '<EMAIL>' => ['password' => 'admin123', 'role' => 'admin', 'name' => 'System Administrator'],
        '<EMAIL>' => ['password' => 'doctor123', 'role' => 'doctor', 'name' => 'Dr. <PERSON>'],
        '<EMAIL>' => ['password' => 'patient123', 'role' => 'patient', 'name' => '<PERSON>']
    ];
    
    if (isset($demo_accounts[$email]) && $demo_accounts[$email]['password'] === $password) {
        $_SESSION['user'] = $demo_accounts[$email];
        $_SESSION['user']['email'] = $email;
        header('Location: ?page=dashboard');
        exit;
    } else {
        $error = 'Invalid credentials';
    }
}

// Logout
if ($_GET['logout'] ?? false) {
    session_destroy();
    header('Location: ?');
    exit;
}

$page = $_GET['page'] ?? 'login';
$user = $_SESSION['user'] ?? null;

if (!$user && $page !== 'login') {
    $page = 'login';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏥 Hospital Management System</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: #2563eb; color: white; padding: 1rem; margin-bottom: 2rem; border-radius: 8px; }
        .nav { display: flex; gap: 1rem; margin-bottom: 2rem; }
        .nav a { background: #2563eb; color: white; padding: 0.5rem 1rem; text-decoration: none; border-radius: 4px; }
        .nav a:hover { background: #1d4ed8; }
        .card { background: white; padding: 2rem; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 2rem; }
        .form-group { margin-bottom: 1rem; }
        .form-group label { display: block; margin-bottom: 0.5rem; font-weight: bold; }
        .form-group input { width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px; }
        .btn { background: #2563eb; color: white; padding: 0.75rem 1.5rem; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background: #1d4ed8; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 2rem; }
        .stat-card { background: white; padding: 1.5rem; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; }
        .stat-number { font-size: 2rem; font-weight: bold; color: #2563eb; }
        .error { color: #dc2626; margin-bottom: 1rem; }
        .demo-accounts { background: #f0f9ff; padding: 1rem; border-radius: 4px; margin-bottom: 1rem; }
        .demo-account { background: white; padding: 0.5rem; margin: 0.5rem 0; border-radius: 4px; cursor: pointer; }
        .demo-account:hover { background: #e5e7eb; }
    </style>
</head>
<body>
    <div class="container">
        <?php if ($user): ?>
            <div class="header">
                <h1>🏥 Hospital Management System</h1>
                <p>Welcome, <?php echo htmlspecialchars($user['name']); ?> (<?php echo ucfirst($user['role']); ?>)</p>
            </div>
            
            <div class="nav">
                <a href="?page=dashboard">Dashboard</a>
                <a href="?page=doctors">Doctors</a>
                <a href="?page=patients">Patients</a>
                <a href="?page=appointments">Appointments</a>
                <a href="?logout=1">Logout</a>
            </div>
        <?php endif; ?>

        <?php if ($page === 'login'): ?>
            <div class="card">
                <h2>🏥 Hospital Management System - Login</h2>
                
                <?php if (isset($error)): ?>
                    <div class="error"><?php echo $error; ?></div>
                <?php endif; ?>
                
                <div class="demo-accounts">
                    <h3>Demo Accounts (Click to auto-fill):</h3>
                    <div class="demo-account" onclick="fillLogin('<EMAIL>', 'admin123')">
                        <strong>Admin:</strong> <EMAIL> / admin123
                    </div>
                    <div class="demo-account" onclick="fillLogin('<EMAIL>', 'doctor123')">
                        <strong>Doctor:</strong> <EMAIL> / doctor123
                    </div>
                    <div class="demo-account" onclick="fillLogin('<EMAIL>', 'patient123')">
                        <strong>Patient:</strong> <EMAIL> / patient123
                    </div>
                </div>
                
                <form method="POST">
                    <input type="hidden" name="login" value="1">
                    <div class="form-group">
                        <label>Email:</label>
                        <input type="email" name="email" id="email" required>
                    </div>
                    <div class="form-group">
                        <label>Password:</label>
                        <input type="password" name="password" id="password" required>
                    </div>
                    <button type="submit" class="btn">Login</button>
                </form>
            </div>

        <?php elseif ($page === 'dashboard'): ?>
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number">150</div>
                    <div>Total Patients</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">25</div>
                    <div>Doctors</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">45</div>
                    <div>Appointments Today</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">8</div>
                    <div>Departments</div>
                </div>
            </div>
            
            <div class="card">
                <h2>📊 Dashboard</h2>
                <p>Welcome to the Hospital Management System dashboard!</p>
                <p>Your role: <strong><?php echo ucfirst($user['role']); ?></strong></p>
            </div>

        <?php elseif ($page === 'doctors'): ?>
            <div class="card">
                <h2>👨‍⚕️ Doctors Management</h2>
                <table style="width: 100%; border-collapse: collapse;">
                    <tr style="background: #f9fafb;">
                        <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e5e7eb;">Name</th>
                        <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e5e7eb;">Specialization</th>
                        <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e5e7eb;">Department</th>
                    </tr>
                    <tr>
                        <td style="padding: 1rem; border-bottom: 1px solid #e5e7eb;">Dr. John Smith</td>
                        <td style="padding: 1rem; border-bottom: 1px solid #e5e7eb;">Cardiology</td>
                        <td style="padding: 1rem; border-bottom: 1px solid #e5e7eb;">Heart Department</td>
                    </tr>
                    <tr>
                        <td style="padding: 1rem; border-bottom: 1px solid #e5e7eb;">Dr. Sarah Wilson</td>
                        <td style="padding: 1rem; border-bottom: 1px solid #e5e7eb;">Neurology</td>
                        <td style="padding: 1rem; border-bottom: 1px solid #e5e7eb;">Brain Department</td>
                    </tr>
                </table>
            </div>

        <?php elseif ($page === 'patients'): ?>
            <div class="card">
                <h2>👥 Patients Management</h2>
                <table style="width: 100%; border-collapse: collapse;">
                    <tr style="background: #f9fafb;">
                        <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e5e7eb;">Name</th>
                        <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e5e7eb;">Age</th>
                        <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e5e7eb;">Phone</th>
                    </tr>
                    <tr>
                        <td style="padding: 1rem; border-bottom: 1px solid #e5e7eb;">Jane Doe</td>
                        <td style="padding: 1rem; border-bottom: 1px solid #e5e7eb;">35</td>
                        <td style="padding: 1rem; border-bottom: 1px solid #e5e7eb;">+**********</td>
                    </tr>
                    <tr>
                        <td style="padding: 1rem; border-bottom: 1px solid #e5e7eb;">John Patient</td>
                        <td style="padding: 1rem; border-bottom: 1px solid #e5e7eb;">42</td>
                        <td style="padding: 1rem; border-bottom: 1px solid #e5e7eb;">+**********</td>
                    </tr>
                </table>
            </div>

        <?php elseif ($page === 'appointments'): ?>
            <div class="card">
                <h2>📅 Appointments</h2>
                <table style="width: 100%; border-collapse: collapse;">
                    <tr style="background: #f9fafb;">
                        <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e5e7eb;">Date</th>
                        <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e5e7eb;">Patient</th>
                        <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e5e7eb;">Doctor</th>
                        <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e5e7eb;">Status</th>
                    </tr>
                    <tr>
                        <td style="padding: 1rem; border-bottom: 1px solid #e5e7eb;">2024-01-15 09:00</td>
                        <td style="padding: 1rem; border-bottom: 1px solid #e5e7eb;">Jane Doe</td>
                        <td style="padding: 1rem; border-bottom: 1px solid #e5e7eb;">Dr. John Smith</td>
                        <td style="padding: 1rem; border-bottom: 1px solid #e5e7eb;"><span style="background: #dbeafe; color: #1e40af; padding: 0.25rem 0.5rem; border-radius: 4px;">Scheduled</span></td>
                    </tr>
                </table>
            </div>
        <?php endif; ?>
    </div>

    <script>
        function fillLogin(email, password) {
            document.getElementById('email').value = email;
            document.getElementById('password').value = password;
        }
    </script>
</body>
</html>
