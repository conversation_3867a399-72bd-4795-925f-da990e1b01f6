using System;
using System.Web.Mvc;
using HospitalSystem.Models;
using System.Collections.Generic;

namespace HospitalSystem.Controllers
{
    public class PatientController : Controller
    {
        private readonly User _user;

        public PatientController()
        {
            _user = new User();
        }

        [Authorize(Roles = "admin,doctor")]
        public ActionResult Index()
        {
            var patients = _user.GetAllUsers("patient");

            ViewBag.PageTitle = "Patient Management";
            ViewBag.PageSubtitle = "Manage patients and their information";
            ViewBag.CurrentPage = "patients";
            ViewBag.Patients = patients;

            return View();
        }
    }
}
