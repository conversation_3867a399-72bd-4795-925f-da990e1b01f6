<?php
/**
 * Authentication Class
 * Hospital Management System
 */

require_once __DIR__ . '/../config/config.php';

class Auth {
    private $db;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }
    
    /**
     * Authenticate user login
     */
    public function login($email, $password) {
        global $demo_accounts;
        
        // Demo mode authentication
        if (DEMO_MODE && isset($demo_accounts[$email])) {
            $demo_user = $demo_accounts[$email];
            if ($password === $demo_user['password']) {
                $this->setUserSession([
                    'id' => $demo_user['id'],
                    'email' => $email,
                    'full_name' => $demo_user['full_name'],
                    'role' => $demo_user['role']
                ]);
                return ['success' => true, 'message' => 'Login successful'];
            } else {
                return ['success' => false, 'message' => 'Invalid demo credentials'];
            }
        }
        
        // Database authentication
        try {
            $query = "SELECT id, email, full_name, role, password_hash FROM user_profiles WHERE email = :email";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':email', $email);
            $stmt->execute();
            
            if ($stmt->rowCount() == 1) {
                $user = $stmt->fetch();
                
                if (password_verify($password, $user['password_hash'])) {
                    $this->setUserSession($user);
                    return ['success' => true, 'message' => 'Login successful'];
                } else {
                    return ['success' => false, 'message' => 'Invalid password'];
                }
            } else {
                return ['success' => false, 'message' => 'User not found'];
            }
        } catch (PDOException $e) {
            return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
        }
    }
    
    /**
     * Register new user
     */
    public function register($email, $password, $full_name, $role = 'patient') {
        if (DEMO_MODE) {
            return ['success' => false, 'message' => 'Registration is not available in demo mode'];
        }
        
        try {
            // Check if user already exists
            $query = "SELECT id FROM user_profiles WHERE email = :email";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':email', $email);
            $stmt->execute();
            
            if ($stmt->rowCount() > 0) {
                return ['success' => false, 'message' => 'User already exists'];
            }
            
            // Hash password
            $password_hash = password_hash($password, PASSWORD_DEFAULT);
            
            // Insert new user
            $query = "INSERT INTO user_profiles (email, full_name, role, password_hash) VALUES (:email, :full_name, :role, :password_hash)";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':email', $email);
            $stmt->bindParam(':full_name', $full_name);
            $stmt->bindParam(':role', $role);
            $stmt->bindParam(':password_hash', $password_hash);
            
            if ($stmt->execute()) {
                return ['success' => true, 'message' => 'Registration successful'];
            } else {
                return ['success' => false, 'message' => 'Registration failed'];
            }
        } catch (PDOException $e) {
            return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
        }
    }
    
    /**
     * Set user session data
     */
    private function setUserSession($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['user_name'] = $user['full_name'];
        $_SESSION['user_role'] = $user['role'];
        $_SESSION['login_time'] = time();
    }
    
    /**
     * Logout user
     */
    public function logout() {
        session_unset();
        session_destroy();
        return ['success' => true, 'message' => 'Logout successful'];
    }
    
    /**
     * Check if user is logged in
     */
    public function isLoggedIn() {
        return isset($_SESSION['user_id']) && isset($_SESSION['user_role']);
    }
    
    /**
     * Get current user data
     */
    public function getCurrentUser() {
        if (!$this->isLoggedIn()) {
            return null;
        }
        
        return [
            'id' => $_SESSION['user_id'],
            'email' => $_SESSION['user_email'],
            'full_name' => $_SESSION['user_name'],
            'role' => $_SESSION['user_role']
        ];
    }
    
    /**
     * Check if user has required role
     */
    public function hasRole($required_role) {
        if (!$this->isLoggedIn()) {
            return false;
        }
        
        $user_role = $_SESSION['user_role'];
        
        // Admin has access to everything
        if ($user_role === 'admin') {
            return true;
        }
        
        // Check specific role
        if (is_array($required_role)) {
            return in_array($user_role, $required_role);
        }
        
        return $user_role === $required_role;
    }
}
?>
