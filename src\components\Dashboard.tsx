import React, { useState, useEffect } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { supabase } from '../lib/supabase'
import { 
  Users, 
  Calendar, 
  UserCheck, 
  Building2, 
  TrendingUp,
  Clock,
  CheckCircle,
  XCircle
} from 'lucide-react'

interface DashboardStats {
  totalPatients: number
  totalDoctors: number
  totalAppointments: number
  totalDepartments: number
  todayAppointments: number
  completedAppointments: number
  cancelledAppointments: number
}

const Dashboard: React.FC = () => {
  const { profile } = useAuth()
  const [stats, setStats] = useState<DashboardStats>({
    totalPatients: 0,
    totalDoctors: 0,
    totalAppointments: 0,
    totalDepartments: 0,
    todayAppointments: 0,
    completedAppointments: 0,
    cancelledAppointments: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    console.log('Dashboard useEffect - fetching stats')
    fetchStats()
  }, [])

  const fetchStats = async () => {
    try {
      console.log('Fetching dashboard stats...')
      const today = new Date().toISOString().split('T')[0]
      
      // Check if we're in demo mode
      const isDemoMode = !import.meta.env.VITE_SUPABASE_URL || 
                        import.meta.env.VITE_SUPABASE_URL === 'https://your-project.supabase.co'

      if (isDemoMode) {
        console.log('Using demo stats')
        // Demo stats
        setStats({
          totalPatients: 1,
          totalDoctors: 2,
          totalAppointments: 1,
          totalDepartments: 5,
          todayAppointments: 0,
          completedAppointments: 0,
          cancelledAppointments: 0
        })
        setLoading(false)
        return
      }

      console.log('Fetching real stats from Supabase')
      const [
        patientsResult,
        doctorsResult,
        appointmentsResult,
        departmentsResult,
        todayAppointmentsResult,
        completedAppointmentsResult,
        cancelledAppointmentsResult
      ] = await Promise.all([
        supabase.from('patients').select('id', { count: 'exact' }),
        supabase.from('doctors').select('id', { count: 'exact' }),
        supabase.from('appointments').select('id', { count: 'exact' }),
        supabase.from('departments').select('id', { count: 'exact' }),
        supabase.from('appointments').select('id', { count: 'exact' }).eq('appointment_date', today),
        supabase.from('appointments').select('id', { count: 'exact' }).eq('status', 'completed'),
        supabase.from('appointments').select('id', { count: 'exact' }).eq('status', 'cancelled')
      ])

      setStats({
        totalPatients: patientsResult.count || 0,
        totalDoctors: doctorsResult.count || 0,
        totalAppointments: appointmentsResult.count || 0,
        totalDepartments: departmentsResult.count || 0,
        todayAppointments: todayAppointmentsResult.count || 0,
        completedAppointments: completedAppointmentsResult.count || 0,
        cancelledAppointments: cancelledAppointmentsResult.count || 0
      })
    } catch (error) {
      console.error('Error fetching stats:', error)
      // Fallback to demo stats on error
      setStats({
        totalPatients: 1,
        totalDoctors: 2,
        totalAppointments: 1,
        totalDepartments: 5,
        todayAppointments: 0,
        completedAppointments: 0,
        cancelledAppointments: 0
      })
    } finally {
      setLoading(false)
    }
  }

  const StatCard: React.FC<{
    title: string
    value: number
    icon: React.ReactNode
    color: string
    trend?: string
  }> = ({ title, value, icon, color, trend }) => (
    <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900 mt-1">{value}</p>
          {trend && (
            <p className="text-sm text-green-600 mt-1 flex items-center">
              <TrendingUp className="w-4 h-4 mr-1" />
              {trend}
            </p>
          )}
        </div>
        <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${color}`}>
          {icon}
        </div>
      </div>
    </div>
  )

  console.log('Dashboard render - loading:', loading, 'profile:', profile)

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">
          Welcome back, {profile?.full_name || 'User'}
        </h1>
        <p className="text-gray-600 mt-2">
          Here's what's happening in your hospital today.
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Patients"
          value={stats.totalPatients}
          icon={<Users className="w-6 h-6 text-blue-600" />}
          color="bg-blue-50"
        />
        <StatCard
          title="Total Doctors"
          value={stats.totalDoctors}
          icon={<UserCheck className="w-6 h-6 text-green-600" />}
          color="bg-green-50"
        />
        <StatCard
          title="Total Appointments"
          value={stats.totalAppointments}
          icon={<Calendar className="w-6 h-6 text-purple-600" />}
          color="bg-purple-50"
        />
        <StatCard
          title="Departments"
          value={stats.totalDepartments}
          icon={<Building2 className="w-6 h-6 text-orange-600" />}
          color="bg-orange-50"
        />
      </div>

      {/* Today's Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <StatCard
          title="Today's Appointments"
          value={stats.todayAppointments}
          icon={<Clock className="w-6 h-6 text-blue-600" />}
          color="bg-blue-50"
        />
        <StatCard
          title="Completed"
          value={stats.completedAppointments}
          icon={<CheckCircle className="w-6 h-6 text-green-600" />}
          color="bg-green-50"
        />
        <StatCard
          title="Cancelled"
          value={stats.cancelledAppointments}
          icon={<XCircle className="w-6 h-6 text-red-600" />}
          color="bg-red-50"
        />
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {profile?.role === 'admin' && (
            <>
              <button className="p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors text-left">
                <UserCheck className="w-8 h-8 text-blue-600 mb-2" />
                <p className="font-medium text-gray-900">Add Doctor</p>
                <p className="text-sm text-gray-600">Register new doctor</p>
              </button>
              <button className="p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors text-left">
                <Users className="w-8 h-8 text-green-600 mb-2" />
                <p className="font-medium text-gray-900">Add Patient</p>
                <p className="text-sm text-gray-600">Register new patient</p>
              </button>
            </>
          )}
          <button className="p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors text-left">
            <Calendar className="w-8 h-8 text-purple-600 mb-2" />
            <p className="font-medium text-gray-900">Schedule Appointment</p>
            <p className="text-sm text-gray-600">Book new appointment</p>
          </button>
          <button className="p-4 bg-orange-50 rounded-lg hover:bg-orange-100 transition-colors text-left">
            <Building2 className="w-8 h-8 text-orange-600 mb-2" />
            <p className="font-medium text-gray-900">View Departments</p>
            <p className="text-sm text-gray-600">Browse all departments</p>
          </button>
        </div>
      </div>
    </div>
  )
}

export default Dashboard