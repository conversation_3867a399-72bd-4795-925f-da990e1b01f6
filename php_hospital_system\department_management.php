<?php
/**
 * Department Management Page
 * Hospital Management System
 */

require_once 'config/config.php';
require_once 'classes/Auth.php';
require_once 'classes/Department.php';

$auth = new Auth();
requireLogin();
requireRole('admin');

$department = new Department();
$current_page = 'departments';
$page_title = 'Department Management';
$page_subtitle = 'Manage hospital departments';

$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token. Please try again.';
    } else {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'create') {
            $name = sanitizeInput($_POST['name'] ?? '');
            $description = sanitizeInput($_POST['description'] ?? '');
            
            if ($department->createDepartment($name, $description)) {
                $message = 'Department created successfully!';
            } else {
                $error = 'Failed to create department. Please try again.';
            }
        } elseif ($action === 'update') {
            $id = sanitizeInput($_POST['department_id'] ?? '');
            $name = sanitizeInput($_POST['name'] ?? '');
            $description = sanitizeInput($_POST['description'] ?? '');
            
            if ($department->updateDepartment($id, $name, $description)) {
                $message = 'Department updated successfully!';
            } else {
                $error = 'Failed to update department. Please try again.';
            }
        } elseif ($action === 'delete') {
            $id = sanitizeInput($_POST['department_id'] ?? '');
            
            if ($department->deleteDepartment($id)) {
                $message = 'Department deleted successfully!';
            } else {
                $error = 'Failed to delete department. Please try again.';
            }
        }
    }
}

// Get departments with stats
$departments = $department->getDepartmentStats();
$csrf_token = generateCSRFToken();

ob_start();
?>

<!-- Messages -->
<?php if ($message): ?>
    <div class="alert alert-success">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="20,6 9,17 4,12"/>
        </svg>
        <?php echo htmlspecialchars($message); ?>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-error">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="10"/>
            <line x1="15" y1="9" x2="9" y2="15"/>
            <line x1="9" y1="9" x2="15" y2="15"/>
        </svg>
        <?php echo htmlspecialchars($error); ?>
    </div>
<?php endif; ?>

<!-- Add Department Section -->
<div class="card" style="margin-bottom: 2rem;">
    <div class="card-body">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <h3>Departments</h3>
            <button type="button" class="btn btn-primary" onclick="showModal('add')">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="12" y1="5" x2="12" y2="19"/>
                    <line x1="5" y1="12" x2="19" y2="12"/>
                </svg>
                Add Department
            </button>
        </div>
    </div>
</div>

<!-- Departments Table -->
<div class="card">
    <div class="card-body">
        <?php if (empty($departments)): ?>
            <p style="text-align: center; color: #6b7280; padding: 2rem;">No departments found.</p>
        <?php else: ?>
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Description</th>
                            <th>Doctors</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($departments as $dept): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($dept['name']); ?></td>
                                <td><?php echo htmlspecialchars($dept['description'] ?? '-'); ?></td>
                                <td><?php echo $dept['doctor_count']; ?></td>
                                <td>
                                    <div style="display: flex; gap: 0.5rem;">
                                        <button type="button" class="btn btn-secondary btn-sm" 
                                                onclick="editDepartment('<?php echo $dept['id']; ?>')">
                                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                                                <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                                            </svg>
                                        </button>
                                        <button type="button" class="btn btn-danger btn-sm" 
                                                onclick="deleteDepartment('<?php echo $dept['id']; ?>', '<?php echo htmlspecialchars($dept['name']); ?>')">
                                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <polyline points="3,6 5,6 21,6"/>
                                                <path d="M19,6v14a2,2 0 0,1-2,2H7a2,2 0 0,1-2-2V6m3,0V4a2,2 0 0,1,2-2h4a2,2 0 0,1,2,2v2"/>
                                            </svg>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Add/Edit Department Modal -->
<div id="departmentModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="modalTitle">Add Department</h3>
            <button type="button" class="modal-close" onclick="hideModal()">&times;</button>
        </div>
        <form id="departmentForm" method="POST">
            <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
            <input type="hidden" name="action" id="formAction" value="create">
            <input type="hidden" name="department_id" id="departmentId" value="">
            
            <div class="modal-body">
                <div class="form-group">
                    <label for="name">Department Name</label>
                    <input type="text" id="name" name="name" required>
                </div>
                
                <div class="form-group">
                    <label for="description">Description</label>
                    <textarea id="description" name="description" rows="3"></textarea>
                </div>
            </div>
            
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal()">Cancel</button>
                <button type="submit" class="btn btn-primary">Save Department</button>
            </div>
        </form>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Confirm Delete</h3>
            <button type="button" class="modal-close" onclick="hideDeleteModal()">&times;</button>
        </div>
        <div class="modal-body">
            <p>Are you sure you want to delete <strong id="deleteDepartmentName"></strong>?</p>
            <p style="color: #dc2626; font-size: 0.875rem;">This action cannot be undone.</p>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="hideDeleteModal()">Cancel</button>
            <form method="POST" style="display: inline;">
                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="department_id" id="deleteDepartmentId" value="">
                <button type="submit" class="btn btn-danger">Delete Department</button>
            </form>
        </div>
    </div>
</div>

<script>
// Department data for editing
const departmentsData = <?php echo json_encode($departments); ?>;

function showModal(action, departmentId = null) {
    const modal = document.getElementById('departmentModal');
    const form = document.getElementById('departmentForm');
    const title = document.getElementById('modalTitle');
    const actionInput = document.getElementById('formAction');
    const departmentIdInput = document.getElementById('departmentId');
    
    if (action === 'add') {
        title.textContent = 'Add Department';
        actionInput.value = 'create';
        departmentIdInput.value = '';
        form.reset();
    } else if (action === 'edit' && departmentId) {
        const dept = departmentsData.find(d => d.id === departmentId);
        if (dept) {
            title.textContent = 'Edit Department';
            actionInput.value = 'update';
            departmentIdInput.value = departmentId;
            
            document.getElementById('name').value = dept.name;
            document.getElementById('description').value = dept.description || '';
        }
    }
    
    modal.style.display = 'flex';
}

function hideModal() {
    document.getElementById('departmentModal').style.display = 'none';
}

function editDepartment(departmentId) {
    showModal('edit', departmentId);
}

function deleteDepartment(departmentId, departmentName) {
    document.getElementById('deleteDepartmentId').value = departmentId;
    document.getElementById('deleteDepartmentName').textContent = departmentName;
    document.getElementById('deleteModal').style.display = 'flex';
}

function hideDeleteModal() {
    document.getElementById('deleteModal').style.display = 'none';
}

// Close modal when clicking outside
window.onclick = function(event) {
    const departmentModal = document.getElementById('departmentModal');
    const deleteModal = document.getElementById('deleteModal');
    
    if (event.target === departmentModal) {
        hideModal();
    }
    if (event.target === deleteModal) {
        hideDeleteModal();
    }
}
</script>

<?php
$content = ob_get_clean();
include 'includes/layout.php';
?>
