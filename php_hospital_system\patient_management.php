<?php
/**
 * Patient Management Page
 * Hospital Management System
 */

require_once 'config/config.php';
require_once 'classes/Auth.php';
require_once 'classes/User.php';

$auth = new Auth();
requireLogin();
requireRole(['admin', 'doctor']);

$user = new User();
$current_page = 'patients';
$page_title = 'Patient Management';
$page_subtitle = 'Manage patients and their information';

// Get patients
$patients = $user->getAllUsers('patient');

ob_start();
?>

<!-- Patients Table -->
<div class="card">
    <div class="card-header">
        <h2 class="card-title">Patients (<?php echo count($patients); ?>)</h2>
    </div>
    <div class="card-body">
        <?php if (empty($patients)): ?>
            <p style="text-align: center; color: #6b7280; padding: 2rem;">No patients found.</p>
        <?php else: ?>
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Registered</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($patients as $patient): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($patient['full_name']); ?></td>
                                <td><?php echo htmlspecialchars($patient['email']); ?></td>
                                <td><?php echo htmlspecialchars($patient['phone'] ?? '-'); ?></td>
                                <td><?php echo date('M j, Y', strtotime($patient['created_at'])); ?></td>
                                <td>
                                    <button type="button" class="btn btn-secondary btn-sm">
                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                            <circle cx="12" cy="12" r="3"/>
                                        </svg>
                                        View
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php
$content = ob_get_clean();
include 'includes/layout.php';
?>
