<?php
echo "<!DOCTYPE html>";
echo "<html>";
echo "<head><title>Test Hospital System</title></head>";
echo "<body>";
echo "<h1>🏥 Hospital System Test</h1>";
echo "<p>PHP is working: " . phpversion() . "</p>";
echo "<p>Current directory: " . __DIR__ . "</p>";

// Check different possible paths
$paths_to_check = [
    __DIR__ . '/php_hospital_system',
    __DIR__ . '/dashboard/php_hospital_system', 
    'C:/xampp/htdocs/php_hospital_system',
    'C:/xampp/htdocs/dashboard/php_hospital_system'
];

echo "<h2>🔍 Checking paths:</h2>";
foreach ($paths_to_check as $path) {
    if (is_dir($path)) {
        echo "<p style='color: green;'>✅ FOUND: " . $path . "</p>";
        $files = scandir($path);
        echo "<ul>";
        foreach ($files as $file) {
            if ($file != '.' && $file != '..') {
                echo "<li>" . $file . "</li>";
            }
        }
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>❌ NOT FOUND: " . $path . "</p>";
    }
}

echo "</body></html>";
?>
