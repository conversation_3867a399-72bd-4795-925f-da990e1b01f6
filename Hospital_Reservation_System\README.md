# Hospital Management System - PHP Version

A complete hospital management system converted from React/TypeScript to PHP and MySQL. This system provides comprehensive functionality for managing doctors, patients, appointments, departments, and rooms.

## Features

- **User Authentication**: Secure login system with role-based access control
- **Dashboard**: Overview with statistics and recent activities
- **Doctor Management**: Add, edit, and manage doctor profiles
- **Patient Management**: View and manage patient information
- **Appointment Management**: Schedule and track appointments
- **Department Management**: Organize hospital departments
- **Room Management**: Track room availability and assignments
- **Profile Management**: User profile settings
- **Demo Mode**: Built-in demo accounts for testing

## User Roles

- **Admin**: Full access to all features
- **Doctor**: Access to patient and appointment management
- **Patient**: View personal appointments and profile

## Demo Accounts

The system includes demo accounts for testing:

- **Admin**: <EMAIL> / admin123
- **Doctor**: <EMAIL> / doctor123
- **Patient**: <EMAIL> / patient123

## Requirements

- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx)
- PDO MySQL extension

## Installation

### 1. Database Setup

1. Create a MySQL database named `hospital_management`
2. Import the database schema:
   ```sql
   mysql -u your_username -p hospital_management < database/schema.sql
   ```
3. Import sample data:
   ```sql
   mysql -u your_username -p hospital_management < database/sample_data.sql
   ```

### 2. Configuration

1. Update database credentials in `config/database.php`:
   ```php
   private $host = 'localhost';
   private $db_name = 'hospital_management';
   private $username = 'your_username';
   private $password = 'your_password';
   ```

2. Update the base URL in `config/config.php`:
   ```php
   define('BASE_URL', 'http://localhost/dashboard/php_hospital_system');
   ```

### 3. Web Server Setup

#### Apache (XAMPP)
1. Copy the project to: `C:\xampp\htdocs\dashboard\php_hospital_system`
2. Ensure Apache is running in XAMPP Control Panel
3. Access via: `http://localhost/dashboard/php_hospital_system/`

#### Nginx
Add this location block to your server configuration:
```nginx
location /dashboard/php_hospital_system {
    try_files $uri $uri/ /dashboard/php_hospital_system/index.php?$query_string;
}
```

## File Structure

```
php_hospital_system/
├── assets/
│   ├── css/
│   │   └── style.css          # Main stylesheet
│   └── js/
│       ├── login.js           # Login page scripts
│       └── main.js            # Main application scripts
├── classes/
│   ├── Auth.php               # Authentication class
│   ├── Dashboard.php          # Dashboard data class
│   ├── Department.php         # Department management
│   ├── Doctor.php             # Doctor management
│   └── User.php               # User management
├── config/
│   ├── config.php             # Main configuration
│   └── database.php           # Database connection
├── database/
│   ├── schema.sql             # Database schema
│   └── sample_data.sql        # Sample data
├── includes/
│   └── layout.php             # Main layout template
├── dashboard.php              # Dashboard page
├── doctor_management.php      # Doctor management
├── login.php                  # Login page
├── logout.php                 # Logout handler
├── profile.php                # User profile
└── index.php                  # Main entry point
```

## Security Features

- **CSRF Protection**: All forms include CSRF tokens
- **SQL Injection Prevention**: Prepared statements used throughout
- **Input Validation**: All user inputs are sanitized
- **Session Management**: Secure session handling
- **Role-based Access Control**: Proper permission checks

## Demo Mode

The system includes a demo mode that works without a database connection. Demo mode features:

- Pre-configured demo accounts
- Sample data for testing
- Limited functionality (no data persistence)
- Automatic fallback when database is unavailable

To disable demo mode, set `DEMO_MODE` to `false` in `config/config.php`.

## Customization

### Adding New Pages

1. Create a new PHP file in the root directory
2. Include the required classes and authentication
3. Set the `$current_page` variable for navigation highlighting
4. Use the layout template for consistent styling

### Modifying Styles

The CSS is organized into sections:
- Base styles and utilities
- Component styles (buttons, forms, etc.)
- Layout styles (sidebar, main content)
- Page-specific styles
- Responsive design

### Adding New User Roles

1. Update the role enum in the database schema
2. Modify the `$menu_items` array in `includes/layout.php`
3. Add role checks to relevant pages using `requireRole()`

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check database credentials in `config/database.php`
   - Ensure MySQL service is running
   - Verify database exists and is accessible

2. **Permission Denied**
   - Check file permissions (755 for directories, 644 for files)
   - Ensure web server has read access to all files

3. **Session Issues**
   - Verify session directory is writable
   - Check PHP session configuration

4. **Styling Issues**
   - Ensure CSS file is accessible
   - Check browser console for 404 errors
   - Verify BASE_URL is correctly set

### Debug Mode

To enable debug mode, add this to `config/config.php`:
```php
ini_set('display_errors', 1);
error_reporting(E_ALL);
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is open source and available under the MIT License.

## Support

For support and questions, please create an issue in the project repository.
