<?php
/**
 * Doctor Model Class
 * Hospital Management System
 */

require_once __DIR__ . '/../config/config.php';

class Doctor {
    private $db;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }
    
    /**
     * Get all doctors with user and department info
     */
    public function getAllDoctors() {
        try {
            $query = "SELECT 
                        d.id, d.specialization, d.license_number, d.experience_years, d.created_at,
                        u.id as user_id, u.email, u.full_name, u.phone,
                        dept.id as department_id, dept.name as department_name
                      FROM doctors d
                      LEFT JOIN user_profiles u ON d.user_id = u.id
                      LEFT JOIN departments dept ON d.department_id = dept.id
                      ORDER BY d.created_at DESC";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            return [];
        }
    }
    
    /**
     * Get doctor by ID
     */
    public function getDoctorById($id) {
        try {
            $query = "SELECT 
                        d.id, d.specialization, d.license_number, d.experience_years, d.created_at,
                        u.id as user_id, u.email, u.full_name, u.phone,
                        dept.id as department_id, dept.name as department_name
                      FROM doctors d
                      LEFT JOIN user_profiles u ON d.user_id = u.id
                      LEFT JOIN departments dept ON d.department_id = dept.id
                      WHERE d.id = :id";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':id', $id);
            $stmt->execute();
            
            return $stmt->fetch();
        } catch (PDOException $e) {
            return null;
        }
    }
    
    /**
     * Create new doctor
     */
    public function createDoctor($userData, $doctorData) {
        try {
            $this->db->beginTransaction();
            
            // Create user profile first
            $userQuery = "INSERT INTO user_profiles (email, full_name, role, phone, password_hash) 
                         VALUES (:email, :full_name, 'doctor', :phone, :password_hash)";
            $userStmt = $this->db->prepare($userQuery);
            $userStmt->bindParam(':email', $userData['email']);
            $userStmt->bindParam(':full_name', $userData['full_name']);
            $userStmt->bindParam(':phone', $userData['phone']);
            $userStmt->bindParam(':password_hash', $userData['password_hash']);
            $userStmt->execute();
            
            $userId = $this->db->lastInsertId();
            
            // Create doctor profile
            $doctorQuery = "INSERT INTO doctors (user_id, department_id, specialization, license_number, experience_years) 
                           VALUES (:user_id, :department_id, :specialization, :license_number, :experience_years)";
            $doctorStmt = $this->db->prepare($doctorQuery);
            $doctorStmt->bindParam(':user_id', $userId);
            $doctorStmt->bindParam(':department_id', $doctorData['department_id']);
            $doctorStmt->bindParam(':specialization', $doctorData['specialization']);
            $doctorStmt->bindParam(':license_number', $doctorData['license_number']);
            $doctorStmt->bindParam(':experience_years', $doctorData['experience_years']);
            $doctorStmt->execute();
            
            $this->db->commit();
            return true;
        } catch (PDOException $e) {
            $this->db->rollBack();
            return false;
        }
    }
    
    /**
     * Update doctor
     */
    public function updateDoctor($id, $userData, $doctorData) {
        try {
            $this->db->beginTransaction();
            
            // Get doctor's user_id
            $doctor = $this->getDoctorById($id);
            if (!$doctor) {
                $this->db->rollBack();
                return false;
            }
            
            // Update user profile
            $userQuery = "UPDATE user_profiles SET full_name = :full_name, phone = :phone, updated_at = CURRENT_TIMESTAMP WHERE id = :user_id";
            $userStmt = $this->db->prepare($userQuery);
            $userStmt->bindParam(':full_name', $userData['full_name']);
            $userStmt->bindParam(':phone', $userData['phone']);
            $userStmt->bindParam(':user_id', $doctor['user_id']);
            $userStmt->execute();
            
            // Update doctor profile
            $doctorQuery = "UPDATE doctors SET 
                           department_id = :department_id, 
                           specialization = :specialization, 
                           license_number = :license_number, 
                           experience_years = :experience_years,
                           updated_at = CURRENT_TIMESTAMP
                           WHERE id = :id";
            $doctorStmt = $this->db->prepare($doctorQuery);
            $doctorStmt->bindParam(':department_id', $doctorData['department_id']);
            $doctorStmt->bindParam(':specialization', $doctorData['specialization']);
            $doctorStmt->bindParam(':license_number', $doctorData['license_number']);
            $doctorStmt->bindParam(':experience_years', $doctorData['experience_years']);
            $doctorStmt->bindParam(':id', $id);
            $doctorStmt->execute();
            
            $this->db->commit();
            return true;
        } catch (PDOException $e) {
            $this->db->rollBack();
            return false;
        }
    }
    
    /**
     * Delete doctor
     */
    public function deleteDoctor($id) {
        try {
            $query = "DELETE FROM doctors WHERE id = :id";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':id', $id);
            
            return $stmt->execute();
        } catch (PDOException $e) {
            return false;
        }
    }
    
    /**
     * Search doctors
     */
    public function searchDoctors($searchTerm) {
        try {
            $query = "SELECT 
                        d.id, d.specialization, d.license_number, d.experience_years, d.created_at,
                        u.id as user_id, u.email, u.full_name, u.phone,
                        dept.id as department_id, dept.name as department_name
                      FROM doctors d
                      LEFT JOIN user_profiles u ON d.user_id = u.id
                      LEFT JOIN departments dept ON d.department_id = dept.id
                      WHERE u.full_name LIKE :search 
                         OR d.specialization LIKE :search 
                         OR dept.name LIKE :search
                      ORDER BY u.full_name";
            
            $searchParam = '%' . $searchTerm . '%';
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':search', $searchParam);
            $stmt->execute();
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            return [];
        }
    }
}
?>
