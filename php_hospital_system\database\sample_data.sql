-- Sample Data for Hospital Management System
USE hospital_management;

-- Insert sample departments
INSERT INTO departments (id, name, description) VALUES
  ('dept-001', 'Cardiology', 'Heart and cardiovascular system'),
  ('dept-002', 'Neurology', 'Brain and nervous system'),
  ('dept-003', 'Orthopedics', 'Bones, joints, and muscles'),
  ('dept-004', 'Pediatrics', 'Children and adolescents'),
  ('dept-005', 'General Medicine', 'General medical care')
ON DUPLICATE KEY UPDATE name=VALUES(name);

-- Insert sample rooms
INSERT INTO rooms (id, room_number, room_type, capacity) VALUES
  ('room-001', '101', 'General Ward', 2),
  ('room-002', '102', 'General Ward', 2),
  ('room-003', '201', 'Private Room', 1),
  ('room-004', '202', 'Private Room', 1),
  ('room-005', '301', 'ICU', 1),
  ('room-006', '302', 'ICU', 1)
ON DUPLICATE KEY UPDATE room_number=VALUES(room_number);

-- Insert demo user profiles (passwords are hashed versions of admin123, doctor123, patient123)
INSERT INTO user_profiles (id, email, full_name, role, phone, password_hash) VALUES
  ('admin-001', '<EMAIL>', 'System Administrator', 'admin', '******-0001', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'),
  ('doctor-001', '<EMAIL>', 'Dr. John Smith', 'doctor', '******-0002', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'),
  ('patient-001', '<EMAIL>', 'Jane Doe', 'patient', '******-0003', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi')
ON DUPLICATE KEY UPDATE email=VALUES(email);

-- Insert additional sample users
INSERT INTO user_profiles (id, email, full_name, role, phone, password_hash) VALUES
  ('doctor-002', '<EMAIL>', 'Dr. Sarah Wilson', 'doctor', '******-0004', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'),
  ('doctor-003', '<EMAIL>', 'Dr. Michael Brown', 'doctor', '******-0005', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'),
  ('patient-002', '<EMAIL>', 'John Patient', 'patient', '******-0006', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'),
  ('patient-003', '<EMAIL>', 'Mary Patient', 'patient', '******-0007', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi')
ON DUPLICATE KEY UPDATE email=VALUES(email);

-- Insert sample doctors
INSERT INTO doctors (id, user_id, department_id, specialization, license_number, experience_years) VALUES
  ('doc-001', 'doctor-001', 'dept-001', 'Interventional Cardiology', 'MD-001-2020', 8),
  ('doc-002', 'doctor-002', 'dept-002', 'Pediatric Neurology', 'MD-002-2018', 10),
  ('doc-003', 'doctor-003', 'dept-003', 'Sports Medicine', 'MD-003-2019', 7)
ON DUPLICATE KEY UPDATE user_id=VALUES(user_id);

-- Insert sample patients
INSERT INTO patients (id, user_id, date_of_birth, gender, address, emergency_contact) VALUES
  ('pat-001', 'patient-001', '1985-06-15', 'female', '123 Main St, City, State 12345', 'Emergency Contact: ******-9999'),
  ('pat-002', 'patient-002', '1978-03-22', 'male', '456 Oak Ave, City, State 12345', 'Emergency Contact: ******-8888'),
  ('pat-003', 'patient-003', '1992-11-08', 'female', '789 Pine Rd, City, State 12345', 'Emergency Contact: ******-7777')
ON DUPLICATE KEY UPDATE user_id=VALUES(user_id);

-- Insert sample appointments
INSERT INTO appointments (id, patient_id, doctor_id, appointment_date, appointment_time, status, notes) VALUES
  ('app-001', 'pat-001', 'doc-001', '2024-01-15', '09:00:00', 'scheduled', 'Regular checkup'),
  ('app-002', 'pat-002', 'doc-002', '2024-01-16', '14:30:00', 'scheduled', 'Follow-up consultation'),
  ('app-003', 'pat-003', 'doc-003', '2024-01-17', '11:00:00', 'completed', 'Sports injury assessment')
ON DUPLICATE KEY UPDATE patient_id=VALUES(patient_id);
