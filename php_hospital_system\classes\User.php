<?php
/**
 * User Model Class
 * Hospital Management System
 */

require_once __DIR__ . '/../config/config.php';

class User {
    private $db;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }
    
    /**
     * Get all users
     */
    public function getAllUsers($role = null) {
        try {
            $query = "SELECT id, email, full_name, role, phone, created_at FROM user_profiles";
            if ($role) {
                $query .= " WHERE role = :role";
            }
            $query .= " ORDER BY created_at DESC";
            
            $stmt = $this->db->prepare($query);
            if ($role) {
                $stmt->bindParam(':role', $role);
            }
            $stmt->execute();
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            return [];
        }
    }
    
    /**
     * Get user by ID
     */
    public function getUserById($id) {
        try {
            $query = "SELECT id, email, full_name, role, phone, created_at FROM user_profiles WHERE id = :id";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':id', $id);
            $stmt->execute();
            
            return $stmt->fetch();
        } catch (PDOException $e) {
            return null;
        }
    }
    
    /**
     * Update user profile
     */
    public function updateUser($id, $data) {
        try {
            $fields = [];
            $params = [':id' => $id];
            
            if (isset($data['full_name'])) {
                $fields[] = "full_name = :full_name";
                $params[':full_name'] = $data['full_name'];
            }
            
            if (isset($data['phone'])) {
                $fields[] = "phone = :phone";
                $params[':phone'] = $data['phone'];
            }
            
            if (isset($data['email'])) {
                $fields[] = "email = :email";
                $params[':email'] = $data['email'];
            }
            
            if (empty($fields)) {
                return false;
            }
            
            $query = "UPDATE user_profiles SET " . implode(', ', $fields) . ", updated_at = CURRENT_TIMESTAMP WHERE id = :id";
            $stmt = $this->db->prepare($query);
            
            return $stmt->execute($params);
        } catch (PDOException $e) {
            return false;
        }
    }
    
    /**
     * Delete user
     */
    public function deleteUser($id) {
        try {
            $query = "DELETE FROM user_profiles WHERE id = :id";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':id', $id);
            
            return $stmt->execute();
        } catch (PDOException $e) {
            return false;
        }
    }
    
    /**
     * Get user statistics
     */
    public function getUserStats() {
        try {
            $stats = [];
            
            // Total users by role
            $query = "SELECT role, COUNT(*) as count FROM user_profiles GROUP BY role";
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            $results = $stmt->fetchAll();
            
            foreach ($results as $result) {
                $stats['total_' . $result['role'] . 's'] = $result['count'];
            }
            
            // Set defaults
            $stats['total_admins'] = $stats['total_admins'] ?? 0;
            $stats['total_doctors'] = $stats['total_doctors'] ?? 0;
            $stats['total_patients'] = $stats['total_patients'] ?? 0;
            
            return $stats;
        } catch (PDOException $e) {
            return [
                'total_admins' => 0,
                'total_doctors' => 0,
                'total_patients' => 0
            ];
        }
    }
}
?>
