<?php
/**
 * Doctor Management Page
 * Hospital Management System
 */

require_once 'config/config.php';
require_once 'classes/Auth.php';
require_once 'classes/Doctor.php';
require_once 'classes/Department.php';

$auth = new Auth();
requireLogin();
requireRole('admin');

$doctor = new Doctor();
$department = new Department();

$current_page = 'doctors';
$page_title = 'Doctor Management';
$page_subtitle = 'Manage doctors and their information';

$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token. Please try again.';
    } else {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'create') {
            $userData = [
                'email' => sanitizeInput($_POST['email'] ?? ''),
                'full_name' => sanitizeInput($_POST['full_name'] ?? ''),
                'phone' => sanitizeInput($_POST['phone'] ?? ''),
                'password_hash' => password_hash('tempPassword123!', PASSWORD_DEFAULT)
            ];
            
            $doctorData = [
                'department_id' => sanitizeInput($_POST['department_id'] ?? ''),
                'specialization' => sanitizeInput($_POST['specialization'] ?? ''),
                'license_number' => sanitizeInput($_POST['license_number'] ?? ''),
                'experience_years' => (int)($_POST['experience_years'] ?? 0)
            ];
            
            if ($doctor->createDoctor($userData, $doctorData)) {
                $message = 'Doctor created successfully!';
            } else {
                $error = 'Failed to create doctor. Please try again.';
            }
        } elseif ($action === 'update') {
            $doctor_id = sanitizeInput($_POST['doctor_id'] ?? '');
            
            $userData = [
                'full_name' => sanitizeInput($_POST['full_name'] ?? ''),
                'phone' => sanitizeInput($_POST['phone'] ?? '')
            ];
            
            $doctorData = [
                'department_id' => sanitizeInput($_POST['department_id'] ?? ''),
                'specialization' => sanitizeInput($_POST['specialization'] ?? ''),
                'license_number' => sanitizeInput($_POST['license_number'] ?? ''),
                'experience_years' => (int)($_POST['experience_years'] ?? 0)
            ];
            
            if ($doctor->updateDoctor($doctor_id, $userData, $doctorData)) {
                $message = 'Doctor updated successfully!';
            } else {
                $error = 'Failed to update doctor. Please try again.';
            }
        } elseif ($action === 'delete') {
            $doctor_id = sanitizeInput($_POST['doctor_id'] ?? '');
            
            if ($doctor->deleteDoctor($doctor_id)) {
                $message = 'Doctor deleted successfully!';
            } else {
                $error = 'Failed to delete doctor. Please try again.';
            }
        }
    }
}

// Get search term
$search_term = sanitizeInput($_GET['search'] ?? '');

// Get doctors
if ($search_term) {
    $doctors = $doctor->searchDoctors($search_term);
} else {
    $doctors = $doctor->getAllDoctors();
}

// Get departments for dropdown
$departments = $department->getAllDepartments();

$csrf_token = generateCSRFToken();

ob_start();
?>

<!-- Messages -->
<?php if ($message): ?>
    <div class="alert alert-success">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="20,6 9,17 4,12"/>
        </svg>
        <?php echo htmlspecialchars($message); ?>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-error">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="10"/>
            <line x1="15" y1="9" x2="9" y2="15"/>
            <line x1="9" y1="9" x2="15" y2="15"/>
        </svg>
        <?php echo htmlspecialchars($error); ?>
    </div>
<?php endif; ?>

<!-- Search and Add Section -->
<div class="card" style="margin-bottom: 2rem;">
    <div class="card-body">
        <div style="display: flex; justify-content: space-between; align-items: center; gap: 1rem; flex-wrap: wrap;">
            <!-- Search Form -->
            <form method="GET" style="display: flex; gap: 0.5rem; flex: 1; max-width: 400px;">
                <input type="text" name="search" placeholder="Search doctors..." 
                       value="<?php echo htmlspecialchars($search_term); ?>"
                       style="flex: 1; padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 0.25rem;">
                <button type="submit" class="btn btn-secondary btn-sm">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="11" cy="11" r="8"/>
                        <path d="M21 21l-4.35-4.35"/>
                    </svg>
                    Search
                </button>
                <?php if ($search_term): ?>
                    <a href="doctor_management.php" class="btn btn-secondary btn-sm">Clear</a>
                <?php endif; ?>
            </form>
            
            <!-- Add Doctor Button -->
            <button type="button" class="btn btn-primary" onclick="showModal('add')">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="12" y1="5" x2="12" y2="19"/>
                    <line x1="5" y1="12" x2="19" y2="12"/>
                </svg>
                Add Doctor
            </button>
        </div>
    </div>
</div>

<!-- Doctors Table -->
<div class="card">
    <div class="card-header">
        <h2 class="card-title">Doctors (<?php echo count($doctors); ?>)</h2>
    </div>
    <div class="card-body">
        <?php if (empty($doctors)): ?>
            <p style="text-align: center; color: #6b7280; padding: 2rem;">
                <?php echo $search_term ? 'No doctors found matching your search.' : 'No doctors found.'; ?>
            </p>
        <?php else: ?>
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Department</th>
                            <th>Specialization</th>
                            <th>License</th>
                            <th>Experience</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($doctors as $doc): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($doc['full_name']); ?></td>
                                <td><?php echo htmlspecialchars($doc['email']); ?></td>
                                <td><?php echo htmlspecialchars($doc['phone'] ?? '-'); ?></td>
                                <td><?php echo htmlspecialchars($doc['department_name'] ?? '-'); ?></td>
                                <td><?php echo htmlspecialchars($doc['specialization']); ?></td>
                                <td><?php echo htmlspecialchars($doc['license_number']); ?></td>
                                <td><?php echo $doc['experience_years']; ?> years</td>
                                <td>
                                    <div style="display: flex; gap: 0.5rem;">
                                        <button type="button" class="btn btn-secondary btn-sm" 
                                                onclick="editDoctor('<?php echo $doc['id']; ?>')">
                                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                                                <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                                            </svg>
                                        </button>
                                        <button type="button" class="btn btn-danger btn-sm" 
                                                onclick="deleteDoctor('<?php echo $doc['id']; ?>', '<?php echo htmlspecialchars($doc['full_name']); ?>')">
                                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <polyline points="3,6 5,6 21,6"/>
                                                <path d="M19,6v14a2,2 0 0,1-2,2H7a2,2 0 0,1-2-2V6m3,0V4a2,2 0 0,1,2-2h4a2,2 0 0,1,2,2v2"/>
                                            </svg>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Add/Edit Doctor Modal -->
<div id="doctorModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="modalTitle">Add Doctor</h3>
            <button type="button" class="modal-close" onclick="hideModal()">&times;</button>
        </div>
        <form id="doctorForm" method="POST">
            <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
            <input type="hidden" name="action" id="formAction" value="create">
            <input type="hidden" name="doctor_id" id="doctorId" value="">
            
            <div class="modal-body">
                <div class="form-group">
                    <label for="email">Email Address</label>
                    <input type="email" id="email" name="email" required>
                </div>
                
                <div class="form-group">
                    <label for="full_name">Full Name</label>
                    <input type="text" id="full_name" name="full_name" required>
                </div>
                
                <div class="form-group">
                    <label for="phone">Phone Number</label>
                    <input type="tel" id="phone" name="phone">
                </div>
                
                <div class="form-group">
                    <label for="department_id">Department</label>
                    <select id="department_id" name="department_id" required>
                        <option value="">Select Department</option>
                        <?php foreach ($departments as $dept): ?>
                            <option value="<?php echo $dept['id']; ?>">
                                <?php echo htmlspecialchars($dept['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="specialization">Specialization</label>
                    <input type="text" id="specialization" name="specialization" required>
                </div>
                
                <div class="form-group">
                    <label for="license_number">License Number</label>
                    <input type="text" id="license_number" name="license_number" required>
                </div>
                
                <div class="form-group">
                    <label for="experience_years">Years of Experience</label>
                    <input type="number" id="experience_years" name="experience_years" min="0" max="50" required>
                </div>
            </div>
            
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal()">Cancel</button>
                <button type="submit" class="btn btn-primary">Save Doctor</button>
            </div>
        </form>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Confirm Delete</h3>
            <button type="button" class="modal-close" onclick="hideDeleteModal()">&times;</button>
        </div>
        <div class="modal-body">
            <p>Are you sure you want to delete <strong id="deleteDoctorName"></strong>?</p>
            <p style="color: #dc2626; font-size: 0.875rem;">This action cannot be undone.</p>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="hideDeleteModal()">Cancel</button>
            <form method="POST" style="display: inline;">
                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="doctor_id" id="deleteDoctorId" value="">
                <button type="submit" class="btn btn-danger">Delete Doctor</button>
            </form>
        </div>
    </div>
</div>

<script>
// Doctor data for editing
const doctorsData = <?php echo json_encode($doctors); ?>;

function showModal(action, doctorId = null) {
    const modal = document.getElementById('doctorModal');
    const form = document.getElementById('doctorForm');
    const title = document.getElementById('modalTitle');
    const actionInput = document.getElementById('formAction');
    const doctorIdInput = document.getElementById('doctorId');
    const emailInput = document.getElementById('email');
    
    if (action === 'add') {
        title.textContent = 'Add Doctor';
        actionInput.value = 'create';
        doctorIdInput.value = '';
        form.reset();
        emailInput.disabled = false;
    } else if (action === 'edit' && doctorId) {
        const doctor = doctorsData.find(d => d.id === doctorId);
        if (doctor) {
            title.textContent = 'Edit Doctor';
            actionInput.value = 'update';
            doctorIdInput.value = doctorId;
            
            document.getElementById('email').value = doctor.email;
            document.getElementById('full_name').value = doctor.full_name;
            document.getElementById('phone').value = doctor.phone || '';
            document.getElementById('department_id').value = doctor.department_id || '';
            document.getElementById('specialization').value = doctor.specialization;
            document.getElementById('license_number').value = doctor.license_number;
            document.getElementById('experience_years').value = doctor.experience_years;
            
            emailInput.disabled = true;
        }
    }
    
    modal.style.display = 'flex';
}

function hideModal() {
    document.getElementById('doctorModal').style.display = 'none';
}

function editDoctor(doctorId) {
    showModal('edit', doctorId);
}

function deleteDoctor(doctorId, doctorName) {
    document.getElementById('deleteDoctorId').value = doctorId;
    document.getElementById('deleteDoctorName').textContent = doctorName;
    document.getElementById('deleteModal').style.display = 'flex';
}

function hideDeleteModal() {
    document.getElementById('deleteModal').style.display = 'none';
}

// Close modal when clicking outside
window.onclick = function(event) {
    const doctorModal = document.getElementById('doctorModal');
    const deleteModal = document.getElementById('deleteModal');
    
    if (event.target === doctorModal) {
        hideModal();
    }
    if (event.target === deleteModal) {
        hideDeleteModal();
    }
}
</script>

<?php
$content = ob_get_clean();
include 'includes/layout.php';
?>
