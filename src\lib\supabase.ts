import { createClient } from '@supabase/supabase-js'

// Check if we have valid Supabase credentials
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

// Demo data for when Supabase is not configured
const demoData = {
  departments: [
    { id: '1', name: 'Cardiology', description: 'Heart and cardiovascular system', created_at: '2024-01-01', updated_at: '2024-01-01' },
    { id: '2', name: 'Neurology', description: 'Brain and nervous system', created_at: '2024-01-01', updated_at: '2024-01-01' },
    { id: '3', name: 'Orthopedics', description: 'Bones, joints, and muscles', created_at: '2024-01-01', updated_at: '2024-01-01' },
    { id: '4', name: 'Pediatrics', description: 'Children and adolescents', created_at: '2024-01-01', updated_at: '2024-01-01' },
    { id: '5', name: 'General Medicine', description: 'General medical care', created_at: '2024-01-01', updated_at: '2024-01-01' }
  ],
  doctors: [
    {
      id: '1',
      user_id: 'demo-doctor-id',
      department_id: '1',
      specialization: 'Cardiology',
      license_number: 'MD001',
      experience_years: 10,
      created_at: '2024-01-01',
      updated_at: '2024-01-01',
      user_profiles: {
        id: 'demo-doctor-id',
        email: '<EMAIL>',
        full_name: 'Dr. Michael Chen',
        phone: '******-0102'
      },
      departments: {
        id: '1',
        name: 'Cardiology'
      }
    },
    {
      id: '2',
      user_id: 'demo-doctor-2',
      department_id: '2',
      specialization: 'Neurology',
      license_number: 'MD002',
      experience_years: 8,
      created_at: '2024-01-01',
      updated_at: '2024-01-01',
      user_profiles: {
        id: 'demo-doctor-2',
        email: '<EMAIL>',
        full_name: 'Dr. Sarah Smith',
        phone: '******-0104'
      },
      departments: {
        id: '2',
        name: 'Neurology'
      }
    }
  ],
  patients: [
    {
      id: '1',
      user_id: 'demo-patient-id',
      date_of_birth: '1990-05-15',
      gender: 'female',
      address: '123 Main St, City, State',
      emergency_contact: '******-0199',
      medical_history: 'No significant medical history',
      created_at: '2024-01-01',
      updated_at: '2024-01-01',
      user_profiles: {
        id: 'demo-patient-id',
        email: '<EMAIL>',
        full_name: 'Emily Davis',
        phone: '******-0103'
      }
    }
  ],
  appointments: [
    {
      id: '1',
      patient_id: '1',
      doctor_id: '1',
      appointment_date: '2024-01-15',
      appointment_time: '10:00',
      status: 'scheduled',
      notes: 'Regular checkup',
      created_at: '2024-01-01',
      updated_at: '2024-01-01'
    }
  ],
  rooms: [
    { id: '1', room_number: '101', room_type: 'General Ward', capacity: 2, is_available: true, created_at: '2024-01-01', updated_at: '2024-01-01' },
    { id: '2', room_number: '102', room_type: 'General Ward', capacity: 2, is_available: true, created_at: '2024-01-01', updated_at: '2024-01-01' },
    { id: '3', room_number: '201', room_type: 'Private Room', capacity: 1, is_available: false, created_at: '2024-01-01', updated_at: '2024-01-01' }
  ]
}

// Helper function to validate URL
const isValidUrl = (url: string): boolean => {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

// Create Supabase client or mock client
const createSupabaseClient = () => {
  // Check if we're in demo mode (no valid credentials or invalid URL)
  const isDemoMode = !supabaseUrl || 
                    !supabaseAnonKey || 
                    supabaseUrl === 'https://your-project.supabase.co' || 
                    supabaseUrl === 'your_supabase_project_url_here' ||
                    supabaseAnonKey === 'your-anon-key' ||
                    supabaseAnonKey === 'your_supabase_anon_key_here' ||
                    !isValidUrl(supabaseUrl)

  if (isDemoMode) {
    console.log('Creating mock Supabase client for demo mode')
    
    // Return a comprehensive mock client for demo purposes
    return {
      auth: {
        getSession: () => {
          console.log('Mock getSession called')
          return Promise.resolve({ data: { session: null }, error: null })
        },
        onAuthStateChange: (callback: any) => {
          console.log('Mock onAuthStateChange called')
          return { data: { subscription: { unsubscribe: () => {} } } }
        },
        signInWithPassword: () => Promise.resolve({ data: null, error: { message: 'Demo mode - Supabase not configured' } }),
        signUp: () => Promise.resolve({ data: null, error: { message: 'Demo mode - Supabase not configured' } }),
        signOut: () => Promise.resolve({ error: null })
      },
      from: (table: string) => {
        console.log(`Mock query for table: ${table}`)
        const tableData = (demoData as any)[table] || []
        
        return {
          select: (columns?: string) => {
            const isCountQuery = columns && columns.includes('count')
            
            const baseQuery = {
              eq: (column: string, value: any) => {
                const filteredData = tableData.filter((item: any) => item[column] === value)
                return {
                  single: () => {
                    const item = filteredData[0]
                    return Promise.resolve({ 
                      data: item || null, 
                      error: item ? null : { message: 'Not found' },
                      count: isCountQuery ? filteredData.length : undefined
                    })
                  },
                  order: (orderColumn?: string, options?: any) => {
                    const sorted = [...filteredData].sort((a: any, b: any) => {
                      const col = orderColumn || 'created_at'
                      if (options?.ascending === false) {
                        return b[col] > a[col] ? 1 : -1
                      }
                      return a[col] > b[col] ? 1 : -1
                    })
                    return Promise.resolve({ 
                      data: sorted, 
                      error: null,
                      count: isCountQuery ? sorted.length : undefined
                    })
                  },
                  count: filteredData.length
                }
              },
              order: (column?: string, options?: any) => {
                const sorted = [...tableData].sort((a: any, b: any) => {
                  const col = column || 'created_at'
                  if (options?.ascending === false) {
                    return b[col] > a[col] ? 1 : -1
                  }
                  return a[col] > b[col] ? 1 : -1
                })
                return Promise.resolve({ 
                  data: sorted, 
                  error: null,
                  count: isCountQuery ? sorted.length : undefined
                })
              },
              count: tableData.length
            }
            
            return baseQuery
          },
          insert: (data: any) => {
            console.log(`Mock insert for ${table}:`, data)
            return Promise.resolve({ data: { ...data, id: Date.now().toString() }, error: null })
          },
          update: (data: any) => ({
            eq: (column: string, value: any) => {
              console.log(`Mock update for ${table}:`, data)
              return Promise.resolve({ data: null, error: null })
            }
          }),
          delete: () => ({
            eq: (column: string, value: any) => {
              console.log(`Mock delete for ${table}`)
              return Promise.resolve({ data: null, error: null })
            }
          })
        }
      }
    } as any
  }
  
  console.log('Creating real Supabase client')
  return createClient(supabaseUrl, supabaseAnonKey)
}

export const supabase = createSupabaseClient()

export type UserRole = 'admin' | 'doctor' | 'patient'

export interface UserProfile {
  id: string
  email: string
  full_name: string
  role: UserRole
  phone?: string
  created_at: string
  updated_at: string
}

export interface Department {
  id: string
  name: string
  description?: string
  created_at: string
  updated_at: string
}

export interface Doctor {
  id: string
  user_id: string
  department_id?: string
  specialization: string
  license_number: string
  experience_years: number
  created_at: string
  updated_at: string
  user_profiles?: UserProfile
  departments?: Department
}

export interface Patient {
  id: string
  user_id: string
  date_of_birth?: string
  gender?: 'male' | 'female' | 'other'
  address?: string
  emergency_contact?: string
  medical_history?: string
  created_at: string
  updated_at: string
  user_profiles?: UserProfile
}

export interface Appointment {
  id: string
  patient_id: string
  doctor_id: string
  appointment_date: string
  appointment_time: string
  status: 'scheduled' | 'completed' | 'cancelled'
  notes?: string
  created_at: string
  updated_at: string
  patients?: Patient
  doctors?: Doctor
}

export interface Room {
  id: string
  room_number: string
  room_type: string
  capacity: number
  is_available: boolean
  created_at: string
  updated_at: string
}

export interface RoomAssignment {
  id: string
  room_id: string
  patient_id: string
  assigned_date: string
  released_date?: string
  status: 'active' | 'released'
  created_at: string
  rooms?: Room
  patients?: Patient
}