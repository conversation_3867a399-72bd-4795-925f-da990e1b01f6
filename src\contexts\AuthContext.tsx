import React, { createContext, useContext, useEffect, useState } from 'react'
import { User, Session } from '@supabase/supabase-js'
import { supabase, UserProfile } from '../lib/supabase'

interface AuthContextType {
  user: User | null
  profile: UserProfile | null
  session: Session | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<void>
  signUp: (email: string, password: string, fullName: string, role: string) => Promise<void>
  signOut: () => Promise<void>
  updateProfile: (updates: Partial<UserProfile>) => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Demo user profiles for when Supabase is not configured
const demoProfiles: Record<string, UserProfile> = {
  '<EMAIL>': {
    id: 'demo-admin-id',
    email: '<EMAIL>',
    full_name: 'Dr. <PERSON>',
    role: 'admin',
    phone: '******-0101',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  '<EMAIL>': {
    id: 'demo-doctor-id',
    email: '<EMAIL>',
    full_name: 'Dr. <PERSON> Chen',
    role: 'doctor',
    phone: '******-0102',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  '<EMAIL>': {
    id: 'demo-patient-id',
    email: '<EMAIL>',
    full_name: 'Emily Davis',
    role: 'patient',
    phone: '******-0103',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
}

// Check if we're in demo mode
const isDemoMode = () => {
  const url = import.meta.env.VITE_SUPABASE_URL
  const key = import.meta.env.VITE_SUPABASE_ANON_KEY
  return !url || !key || url === 'https://your-project.supabase.co' || key === 'your-anon-key'
}

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    console.log('AuthProvider initializing...')
    
    if (isDemoMode()) {
      console.log('Running in demo mode')
      // In demo mode, check for stored demo session
      const demoSession = localStorage.getItem('demo-session')
      if (demoSession) {
        try {
          const profileData = JSON.parse(demoSession)
          console.log('Found demo session:', profileData)
          setProfile(profileData)
          setUser({ id: profileData.id, email: profileData.email } as User)
        } catch (e) {
          console.error('Error parsing demo session:', e)
          localStorage.removeItem('demo-session')
        }
      }
      setLoading(false)
      return
    }

    // Real Supabase mode
    console.log('Running with real Supabase')
    
    const initAuth = async () => {
      try {
        // Get initial session
        const { data: { session }, error } = await supabase.auth.getSession()
        if (error) {
          console.error('Error getting session:', error)
        } else {
          console.log('Initial session:', session)
          setSession(session)
          setUser(session?.user ?? null)
          if (session?.user) {
            await fetchProfile(session.user.id)
          }
        }
      } catch (error) {
        console.error('Error in initAuth:', error)
      } finally {
        setLoading(false)
      }
    }

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session)
        setSession(session)
        setUser(session?.user ?? null)
        
        if (session?.user) {
          await fetchProfile(session.user.id)
        } else {
          setProfile(null)
        }
      }
    )

    initAuth()

    return () => {
      console.log('Cleaning up auth subscription')
      subscription.unsubscribe()
    }
  }, [])

  const fetchProfile = async (userId: string) => {
    try {
      console.log('Fetching profile for user:', userId)
      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single()

      if (error) {
        console.error('Error fetching profile:', error)
      } else {
        console.log('Profile fetched:', data)
        setProfile(data)
      }
    } catch (error) {
      console.error('Error in fetchProfile:', error)
    }
  }

  const signIn = async (email: string, password: string) => {
    console.log('SignIn attempt:', email)
    
    if (isDemoMode()) {
      // Demo mode authentication
      const demoProfile = demoProfiles[email]
      if (demoProfile && (password === 'admin123' || password === 'doctor123' || password === 'patient123')) {
        console.log('Demo login successful:', demoProfile)
        setProfile(demoProfile)
        setUser({ id: demoProfile.id, email: demoProfile.email } as User)
        localStorage.setItem('demo-session', JSON.stringify(demoProfile))
        return
      } else {
        throw new Error('Invalid demo credentials. Use the demo accounts provided.')
      }
    }

    const { error } = await supabase.auth.signInWithPassword({
      email,
      password
    })
    if (error) throw error
  }

  const signUp = async (email: string, password: string, fullName: string, role: string) => {
    if (isDemoMode()) {
      throw new Error('Sign up is not available in demo mode. Please use the demo accounts provided.')
    }

    const { data, error } = await supabase.auth.signUp({
      email,
      password
    })
    
    if (error) throw error
    
    if (data.user) {
      const { error: profileError } = await supabase
        .from('user_profiles')
        .insert({
          id: data.user.id,
          email,
          full_name: fullName,
          role: role as any
        })
      
      if (profileError) throw profileError
    }
  }

  const signOut = async () => {
    console.log('SignOut attempt')
    
    if (isDemoMode()) {
      setUser(null)
      setProfile(null)
      localStorage.removeItem('demo-session')
      return
    }

    const { error } = await supabase.auth.signOut()
    if (error) throw error
  }

  const updateProfile = async (updates: Partial<UserProfile>) => {
    if (!user) throw new Error('No user logged in')
    
    if (isDemoMode()) {
      throw new Error('Profile updates are not available in demo mode.')
    }
    
    const { error } = await supabase
      .from('user_profiles')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', user.id)
    
    if (error) throw error
    
    // Refresh profile
    await fetchProfile(user.id)
  }

  const value = {
    user,
    profile,
    session,
    loading,
    signIn,
    signUp,
    signOut,
    updateProfile
  }

  console.log('AuthProvider render - loading:', loading, 'user:', user, 'profile:', profile)

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}