using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using HospitalSystem.Config; // Assume you have a config file for DB connection

namespace HospitalSystem.Models
{
    public class User
    {
        private readonly SqlConnection _connection;

        public User()
        {
            Database database = new Database();
            _connection = database.GetConnection();
        }

        // Get all users (optionally by role)
        public List<Dictionary<string, object>> GetAllUsers(string role = null)
        {
            var users = new List<Dictionary<string, object>>();
            try
            {
                _connection.Open();
                string query = "SELECT id, email, full_name, role, phone, created_at FROM user_profiles";
                if (!string.IsNullOrEmpty(role))
                {
                    query += " WHERE role = @role";
                }
                query += " ORDER BY created_at DESC";

                using (SqlCommand cmd = new SqlCommand(query, _connection))
                {
                    if (!string.IsNullOrEmpty(role))
                    {
                        cmd.Parameters.AddWithValue("@role", role);
                    }

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var user = new Dictionary<string, object>();
                            for (int i = 0; i < reader.FieldCount; i++)
                            {
                                user[reader.GetName(i)] = reader.GetValue(i);
                            }
                            users.Add(user);
                        }
                    }
                }
            }
            catch
            {
                // Log exception if needed
            }
            finally
            {
                _connection.Close();
            }

            return users;
        }

        // Get user by ID
        public Dictionary<string, object> GetUserById(int id)
        {
            Dictionary<string, object> user = null;
            try
            {
                _connection.Open();
                string query = "SELECT id, email, full_name, role, phone, created_at FROM user_profiles WHERE id = @id";

                using (SqlCommand cmd = new SqlCommand(query, _connection))
                {
                    cmd.Parameters.AddWithValue("@id", id);

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            user = new Dictionary<string, object>();
                            for (int i = 0; i < reader.FieldCount; i++)
                            {
                                user[reader.GetName(i)] = reader.GetValue(i);
                            }
                        }
                    }
                }
            }
            catch
            {
                // Log exception
            }
            finally
            {
                _connection.Close();
            }

            return user;
        }

        // Update user by ID
        public bool UpdateUser(int id, Dictionary<string, string> data)
        {
            try
            {
                _connection.Open();

                var fields = new List<string>();
                var cmd = new SqlCommand();
                cmd.Connection = _connection;

                if (data.ContainsKey("full_name"))
                {
                    fields.Add("full_name = @full_name");
                    cmd.Parameters.AddWithValue("@full_name", data["full_name"]);
                }
                if (data.ContainsKey("phone"))
                {
                    fields.Add("phone = @phone");
                    cmd.Parameters.AddWithValue("@phone", data["phone"]);
                }
                if (data.ContainsKey("email"))
                {
                    fields.Add("email = @email");
                    cmd.Parameters.AddWithValue("@email", data["email"]);
                }

                if (fields.Count == 0) return false;

                string query = "UPDATE user_profiles SET " + string.Join(", ", fields) + ", updated_at = CURRENT_TIMESTAMP WHERE id = @id";
                cmd.CommandText = query;
                cmd.Parameters.AddWithValue("@id", id);

                return cmd.ExecuteNonQuery() > 0;
            }
            catch
            {
                return false;
            }
            finally
            {
                _connection.Close();
            }
        }

        // Delete user by ID
        public bool DeleteUser(int id)
        {
            try
            {
                _connection.Open();
                string query = "DELETE FROM user_profiles WHERE id = @id";

                using (SqlCommand cmd = new SqlCommand(query, _connection))
                {
                    cmd.Parameters.AddWithValue("@id", id);
                    return cmd.ExecuteNonQuery() > 0;
                }
            }
            catch
            {
                return false;
            }
            finally
            {
                _connection.Close();
            }
        }

        // Get user statistics
        public Dictionary<string, int> GetUserStats()
        {
            var stats = new Dictionary<string, int>
            {
                { "total_admins", 0 },
                { "total_doctors", 0 },
                { "total_patients", 0 }
            };

            try
            {
                _connection.Open();
                string query = "SELECT role, COUNT(*) as count FROM user_profiles GROUP BY role";

                using (SqlCommand cmd = new SqlCommand(query, _connection))
                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        string role = reader["role"].ToString();
                        int count = Convert.ToInt32(reader["count"]);

                        stats["total_" + role + "s"] = count;
                    }
                }
            }
            catch
            {
                // Return default stats
            }
            finally
            {
                _connection.Close();
            }

            return stats;
        }
    }
}
