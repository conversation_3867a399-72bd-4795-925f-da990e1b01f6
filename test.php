<?php
echo "<h1>PHP Test Page</h1>";
echo "<p>PHP is working!</p>";
echo "<p>Current time: " . date('Y-m-d H:i:s') . "</p>";
echo "<p>PHP Version: " . phpversion() . "</p>";

// Test if we can access the hospital system
$hospital_path = __DIR__ . '/php_hospital_system';
if (is_dir($hospital_path)) {
    echo "<p style='color: green;'>✅ Hospital system folder found!</p>";
    echo "<p><a href='/php_hospital_system/login.php'>Go to Hospital System</a></p>";
} else {
    echo "<p style='color: red;'>❌ Hospital system folder NOT found!</p>";
    echo "<p>Looking for: " . $hospital_path . "</p>";
}

// List files in htdocs
echo "<h2>Files in htdocs:</h2>";
$files = scandir(__DIR__);
foreach ($files as $file) {
    if ($file != '.' && $file != '..') {
        echo "<p>📁 " . $file . "</p>";
    }
}
?>
