<?php
/**
 * Setup Script for Hospital Management System
 * This script helps with initial setup and database configuration
 */

// Check if setup is already completed
if (file_exists('config/.setup_complete')) {
    die('Setup has already been completed. Delete config/.setup_complete to run setup again.');
}

$step = $_GET['step'] ?? 1;
$error = '';
$success = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($step == 2) {
        // Database configuration
        $host = $_POST['host'] ?? 'localhost';
        $dbname = $_POST['dbname'] ?? 'hospital_management';
        $username = $_POST['username'] ?? 'root';
        $password = $_POST['password'] ?? '';
        
        // Test database connection
        try {
            $pdo = new PDO("mysql:host=$host", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // Create database if it doesn't exist
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname`");
            $pdo->exec("USE `$dbname`");
            
            // Update database configuration
            $config_content = file_get_contents('config/database.php');
            $config_content = str_replace("'localhost'", "'$host'", $config_content);
            $config_content = str_replace("'hospital_management'", "'$dbname'", $config_content);
            $config_content = str_replace("'root'", "'$username'", $config_content);
            $config_content = str_replace("'';", "'$password';", $config_content);

            file_put_contents('config/database.php', $config_content);

            // Also update BASE_URL in config.php
            $main_config = file_get_contents('config/config.php');
            $main_config = str_replace(
                "define('BASE_URL', 'http://localhost/php_hospital_system');",
                "define('BASE_URL', 'http://localhost/dashboard/php_hospital_system');",
                $main_config
            );
            file_put_contents('config/config.php', $main_config);
            
            $success = 'Database configuration saved successfully!';
            $step = 3;
        } catch (PDOException $e) {
            $error = 'Database connection failed: ' . $e->getMessage();
        }
    } elseif ($step == 3) {
        // Import database schema and data
        try {
            require_once 'config/database.php';
            $database = new Database();
            $pdo = $database->getConnection();
            
            // Read and execute schema
            $schema = file_get_contents('database/schema.sql');
            $pdo->exec($schema);
            
            // Read and execute sample data
            $sample_data = file_get_contents('database/sample_data.sql');
            $pdo->exec($sample_data);
            
            // Mark setup as complete
            file_put_contents('config/.setup_complete', date('Y-m-d H:i:s'));
            
            $success = 'Database setup completed successfully!';
            $step = 4;
        } catch (Exception $e) {
            $error = 'Database setup failed: ' . $e->getMessage();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hospital Management System - Setup</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #dbeafe 0%, #e0e7ff 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .setup-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            width: 60px;
            height: 60px;
            background: #2563eb;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 24px;
        }
        h1 { color: #111827; margin: 0 0 10px; }
        .subtitle { color: #6b7280; margin: 0; }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
            color: white;
        }
        .step.active { background: #2563eb; }
        .step.completed { background: #059669; }
        .step.pending { background: #d1d5db; color: #6b7280; }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #374151;
        }
        input, select {
            width: 100%;
            padding: 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            box-sizing: border-box;
        }
        input:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        .btn {
            background: #2563eb;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover { background: #1d4ed8; }
        .btn-secondary {
            background: #6b7280;
        }
        .btn-secondary:hover { background: #4b5563; }
        .alert {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .alert-success {
            background: #f0fdf4;
            color: #059669;
            border: 1px solid #bbf7d0;
        }
        .alert-error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        .demo-info {
            background: #fffbeb;
            border: 1px solid #fed7aa;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }
        .demo-info h3 {
            margin: 0 0 10px;
            color: #d97706;
        }
        .demo-accounts {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        .demo-account {
            background: white;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="header">
            <div class="logo">🏥</div>
            <h1>Hospital Management System</h1>
            <p class="subtitle">Setup Wizard</p>
        </div>

        <div class="step-indicator">
            <div class="step <?php echo $step >= 1 ? ($step > 1 ? 'completed' : 'active') : 'pending'; ?>">1</div>
            <div class="step <?php echo $step >= 2 ? ($step > 2 ? 'completed' : 'active') : 'pending'; ?>">2</div>
            <div class="step <?php echo $step >= 3 ? ($step > 3 ? 'completed' : 'active') : 'pending'; ?>">3</div>
            <div class="step <?php echo $step >= 4 ? 'active' : 'pending'; ?>">4</div>
        </div>

        <?php if ($error): ?>
            <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>

        <?php if ($success): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($success); ?></div>
        <?php endif; ?>

        <?php if ($step == 1): ?>
            <h2>Welcome to Setup</h2>
            <p>This wizard will help you set up your Hospital Management System. We'll configure the database and import sample data.</p>
            
            <div class="demo-info">
                <h3>Demo Accounts</h3>
                <p>The system includes these demo accounts for testing:</p>
                <div class="demo-accounts">
                    <div class="demo-account">
                        <strong>Admin</strong><br>
                        <EMAIL><br>
                        Password: admin123
                    </div>
                    <div class="demo-account">
                        <strong>Doctor</strong><br>
                        <EMAIL><br>
                        Password: doctor123
                    </div>
                    <div class="demo-account">
                        <strong>Patient</strong><br>
                        <EMAIL><br>
                        Password: patient123
                    </div>
                </div>
            </div>

            <a href="?step=2" class="btn">Start Setup</a>

        <?php elseif ($step == 2): ?>
            <h2>Database Configuration</h2>
            <p>Enter your database connection details:</p>

            <form method="POST">
                <div class="form-group">
                    <label for="host">Database Host</label>
                    <input type="text" id="host" name="host" value="localhost" required>
                </div>

                <div class="form-group">
                    <label for="dbname">Database Name</label>
                    <input type="text" id="dbname" name="dbname" value="hospital_management" required>
                </div>

                <div class="form-group">
                    <label for="username">Username</label>
                    <input type="text" id="username" name="username" value="root" required>
                </div>

                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password">
                </div>

                <button type="submit" class="btn">Test Connection & Continue</button>
            </form>

        <?php elseif ($step == 3): ?>
            <h2>Import Database</h2>
            <p>Database connection successful! Now we'll create the tables and import sample data.</p>

            <form method="POST">
                <button type="submit" class="btn">Import Database Schema & Data</button>
            </form>

        <?php elseif ($step == 4): ?>
            <h2>Setup Complete!</h2>
            <p>Your Hospital Management System has been successfully set up. You can now log in using the demo accounts.</p>

            <div style="text-align: center; margin-top: 30px;">
                <a href="login.php" class="btn">Go to Login Page</a>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
