<?php
/**
 * Login Page
 * Hospital Management System
 */

require_once 'config/config.php';
require_once 'classes/Auth.php';

$auth = new Auth();

// Redirect if already logged in
if ($auth->isLoggedIn()) {
    redirect(BASE_URL . '/dashboard.php');
}

$error = '';
$success = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token. Please try again.';
    } else {
        $action = $_POST['action'] ?? 'login';
        $email = sanitizeInput($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        
        if ($action === 'login') {
            if (empty($email) || empty($password)) {
                $error = 'Please fill in all fields.';
            } else {
                $result = $auth->login($email, $password);
                if ($result['success']) {
                    redirect(BASE_URL . '/dashboard.php');
                } else {
                    $error = $result['message'];
                }
            }
        } elseif ($action === 'register') {
            $full_name = sanitizeInput($_POST['full_name'] ?? '');
            $role = sanitizeInput($_POST['role'] ?? 'patient');
            
            if (empty($email) || empty($password) || empty($full_name)) {
                $error = 'Please fill in all fields.';
            } else {
                $result = $auth->register($email, $password, $full_name, $role);
                if ($result['success']) {
                    $success = $result['message'] . ' You can now log in.';
                } else {
                    $error = $result['message'];
                }
            }
        }
    }
}

$csrf_token = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?> - Login</title>
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body class="login-page">
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="logo">
                    <div class="logo-icon">
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M22 12h-4l-3 9L9 3l-3 9H2"/>
                        </svg>
                    </div>
                </div>
                <h1><?php echo APP_NAME; ?></h1>
                <p>Hospital Management System</p>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-error">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="10"/>
                        <line x1="15" y1="9" x2="9" y2="15"/>
                        <line x1="9" y1="9" x2="15" y2="15"/>
                    </svg>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="20,6 9,17 4,12"/>
                    </svg>
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>

            <div class="login-tabs">
                <button class="tab-button active" onclick="showTab('login')">Sign In</button>
                <?php if (!DEMO_MODE): ?>
                <button class="tab-button" onclick="showTab('register')">Sign Up</button>
                <?php endif; ?>
            </div>

            <!-- Login Form -->
            <form id="login-form" method="POST" class="login-form active">
                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                <input type="hidden" name="action" value="login">
                
                <div class="form-group">
                    <label for="login_email">Email Address</label>
                    <input type="email" id="login_email" name="email" required>
                </div>

                <div class="form-group">
                    <label for="login_password">Password</label>
                    <div class="password-input">
                        <input type="password" id="login_password" name="password" required>
                        <button type="button" class="password-toggle" onclick="togglePassword('login_password')">
                            <svg class="eye-open" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                <circle cx="12" cy="12" r="3"/>
                            </svg>
                            <svg class="eye-closed" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="display: none;">
                                <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                                <line x1="1" y1="1" x2="23" y2="23"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <button type="submit" class="btn btn-primary btn-full">Sign In</button>
            </form>

            <!-- Register Form -->
            <?php if (!DEMO_MODE): ?>
            <form id="register-form" method="POST" class="login-form">
                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                <input type="hidden" name="action" value="register">
                
                <div class="form-group">
                    <label for="register_full_name">Full Name</label>
                    <input type="text" id="register_full_name" name="full_name" required>
                </div>

                <div class="form-group">
                    <label for="register_email">Email Address</label>
                    <input type="email" id="register_email" name="email" required>
                </div>

                <div class="form-group">
                    <label for="register_password">Password</label>
                    <div class="password-input">
                        <input type="password" id="register_password" name="password" required>
                        <button type="button" class="password-toggle" onclick="togglePassword('register_password')">
                            <svg class="eye-open" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                <circle cx="12" cy="12" r="3"/>
                            </svg>
                            <svg class="eye-closed" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="display: none;">
                                <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                                <line x1="1" y1="1" x2="23" y2="23"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="form-group">
                    <label for="register_role">Role</label>
                    <select id="register_role" name="role" required>
                        <option value="patient">Patient</option>
                        <option value="doctor">Doctor</option>
                    </select>
                </div>

                <button type="submit" class="btn btn-primary btn-full">Create Account</button>
            </form>
            <?php endif; ?>

            <!-- Demo Accounts -->
            <div class="demo-accounts">
                <h3>Demo Accounts</h3>
                <div class="demo-list">
                    <button type="button" class="demo-account" onclick="setDemoCredentials('<EMAIL>', 'admin123')">
                        <strong>Admin:</strong> <EMAIL>
                    </button>
                    <button type="button" class="demo-account" onclick="setDemoCredentials('<EMAIL>', 'doctor123')">
                        <strong>Doctor:</strong> <EMAIL>
                    </button>
                    <button type="button" class="demo-account" onclick="setDemoCredentials('<EMAIL>', 'patient123')">
                        <strong>Patient:</strong> <EMAIL>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/login.js"></script>
</body>
</html>
